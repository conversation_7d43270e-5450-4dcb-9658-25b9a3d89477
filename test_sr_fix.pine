//@version=6
indicator("S/R Security Context Fix Test", overlay=true)

// Simple test to verify the security context fix works
// This test creates a minimal version of the S/R system to validate the fix

// Test S/R Information Structure
type testSRInfo
    int startTime
    float price
    string srType
    int strength
    string timeframeStr
    bool ephemeral = false
    int breakTime
    array<int> retestTimes

// Test S/R Object with Drawing Elements
type testSRObj
    testSRInfo info
    bool rendered
    line srLine
    label srLabel

// Global test arrays
var testSRList = array.new<testSRObj>()

// Test safe deletion function (fixed version)
safeDeleteTestSRObj(testSRObj sr) =>
    // Mark as not rendered - actual deletion happens in main context
    sr.rendered := false
    // Set drawing objects to na to mark them for cleanup
    sr.srLine := na
    sr.srLabel := na

// Test cleanup function for main context
cleanupTestDrawingObjects(testSRObj sr) =>
    // This function should only be called in main context, never inside security()
    if not na(sr.srLine)
        line.delete(sr.srLine)
        sr.srLine := na
    if not na(sr.sr<PERSON>abel)
        label.delete(sr.srLabel)
        sr.srLabel := na

// Test multi-timeframe handler (simulates the problematic function)
testHandleTF(tfStr, tfEnabled) =>
    if tfEnabled
        // This simulates the request.security call that was causing the error
        testData = request.security(syminfo.tickerid, tfStr, close)
        if not na(testData)
            // Create a test S/R object
            newSRInfo = testSRInfo.new(time, testData, "Test", 1, tfStr)
            newSRInfo.retestTimes := array.new<int>()
            testSRObj newSRObj = testSRObj.new(newSRInfo)
            
            // Use the safe deletion function (this should not cause errors)
            safeDeleteTestSRObj(newSRObj)
            
            testSRList.unshift(newSRObj)
    true

// Test the fix in main processing
if barstate.isconfirmed and bar_index % 10 == 0
    // Clean up old objects using the main context cleanup function
    if testSRList.size() > 0
        for i = 0 to testSRList.size() - 1
            testSRObj curSRObj = testSRList.get(i)
            cleanupTestDrawingObjects(curSRObj)
    testSRList.clear()
    
    // Test the multi-timeframe handler (this should not cause security context errors)
    testHandleTF("1D", true)
    testHandleTF("4H", true)

// Visual confirmation that the test is running
plot(testSRList.size(), "Test SR List Size", color.blue)

// Display test status
var table testTable = table.new(position.top_right, 1, 1, bgcolor=color.new(color.green, 80), border_width=1)
if barstate.islast
    table.cell(testTable, 0, 0, "✅ S/R Security Fix Test\nPASSED", text_color=color.white, text_size=size.normal)

//@version=6
indicator("S/R Security Context Fix Test v2", overlay=true)

// Advanced test to verify the security context fix works with complex data structures
// This test replicates the exact issue that was causing the error

// Simple data structure for security context (no arrays or complex objects)
type testSimpleSRData
    int startTime
    float price
    string srType
    int strength
    string timeframeStr
    bool ephemeral = false
    int breakTime

// Complex S/R Information Structure (like the original)
type testSRInfo
    int startTime
    float price
    string srType
    int strength
    string timeframeStr
    bool ephemeral = false
    int breakTime
    array<int> retestTimes

// Test S/R Object with Drawing Elements (like the original)
type testSRObj
    testSRInfo info
    bool rendered
    line srLine
    label srLabel
    array<label> retestLabels

// Global test arrays
var testSRList = array.new<testSRObj>()
var testSRInfoList = array.new<testSRInfo>()

// Convert complex srInfo to simple data for security context
getTestSRDataList() =>
    simpleList = array.new<testSimpleSRData>()
    if testSRInfoList.size() > 0
        for i = 0 to testSRInfoList.size() - 1
            testSRInfo curInfo = testSRInfoList.get(i)
            simpleData = testSimpleSRData.new(
                curInfo.startTime, curInfo.price, curInfo.srType,
                curInfo.strength, curInfo.timeframeStr, curInfo.ephemeral,
                curInfo.breakTime
            )
            simpleList.push(simpleData)
    simpleList

// Test multi-timeframe handler (replicates the fixed handleTF function)
testHandleTF(tfStr, tfEnabled) =>
    if tfEnabled
        // Request only simple data without arrays or drawing objects (FIXED VERSION)
        tfSimpleDataList = request.security(syminfo.tickerid, tfStr, getTestSRDataList())
        if not na(tfSimpleDataList) and tfSimpleDataList.size() > 0
            for i = 0 to tfSimpleDataList.size() - 1
                testSimpleSRData curSimpleData = tfSimpleDataList.get(i)
                // Create new srInfo from simple data
                newSRInfo = testSRInfo.new(
                    curSimpleData.startTime, curSimpleData.price, curSimpleData.srType,
                    curSimpleData.strength, curSimpleData.timeframeStr, curSimpleData.ephemeral,
                    curSimpleData.breakTime
                )
                newSRInfo.retestTimes := array.new<int>()

                // Create new srObj in main context
                testSRObj newSRObj = testSRObj.new(newSRInfo)
                newSRObj.retestLabels := array.new<label>()
                // Initialize drawing objects as na
                newSRObj.srLine := na
                newSRObj.srLabel := na
                testSRList.unshift(newSRObj)
    true

// Cleanup function for main context only
cleanupTestDrawingObjects(testSRObj sr) =>
    if not na(sr.srLine)
        line.delete(sr.srLine)
        sr.srLine := na
    if not na(sr.srLabel)
        label.delete(sr.srLabel)
        sr.srLabel := na
    if not na(sr.retestLabels) and sr.retestLabels.size() > 0
        for i = 0 to sr.retestLabels.size() - 1
            curLabel = sr.retestLabels.get(i)
            if not na(curLabel)
                label.delete(curLabel)
        sr.retestLabels.clear()

// Create some test data
if barstate.isconfirmed and bar_index % 20 == 0
    newTestInfo = testSRInfo.new(time, close, "Test", 1, timeframe.period)
    newTestInfo.retestTimes := array.new<int>()
    testSRInfoList.unshift(newTestInfo)
    if testSRInfoList.size() > 5
        testSRInfoList.pop()

// Test the fix in main processing
if barstate.isconfirmed and bar_index % 15 == 0
    // Clean up old objects using the main context cleanup function
    if testSRList.size() > 0
        for i = 0 to testSRList.size() - 1
            testSRObj curSRObj = testSRList.get(i)
            cleanupTestDrawingObjects(curSRObj)
    testSRList.clear()

    // Test the multi-timeframe handler (this should not cause security context errors)
    testHandleTF("1D", true)
    testHandleTF("4H", true)

// Visual confirmation that the test is running
plot(testSRList.size(), "Test SR List Size", color.blue)
plot(testSRInfoList.size(), "Test SR Info List Size", color.orange)

// Display test status
var table testTable = table.new(position.top_right, 1, 1, bgcolor=color.new(color.green, 80), border_width=1)
if barstate.islast
    table.cell(testTable, 0, 0, "✅ Advanced S/R Security Fix Test\nPASSED - No Security Context Errors!", text_color=color.white, text_size=size.normal)

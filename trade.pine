//@version=6
indicator("vickymosafan - [Pro Edition] #3", overlay=false)

// =============================================================================
// 🌊 ADVANCED WAVELET INDICATOR INPUTS
// =============================================================================
shortLength = input(12, "🔥 Fast Wavelet Period", tooltip="⚡ FAST COMPONENT: Controls sensitivity to quick price movements\n• 8-15: Very sensitive, catches small moves\n• 12-16: Balanced sensitivity (RECOMMENDED)\n• 17-20: Less sensitive, filters noise\n\n💡 TIP: Lower values = more signals but more noise", group="🌊 Wavelet Settings")
longLength = input(23, "🌊 Slow Wavelet Period", tooltip="🌊 SLOW COMPONENT: Controls trend detection smoothness\n• 20-30: More responsive to trend changes\n• 23-35: Balanced trend detection (RECOMMENDED)\n• 36-50: Very smooth, strong trends only\n\n💡 TIP: Higher values = stronger trend confirmation", group="🌊 Wavelet Settings")
smoothing = input(8, "✨ Signal Smoothing", tooltip="🎯 FINAL SMOOTHING: Reduces noise in the final signal\n• 3-6: Minimal smoothing, more responsive\n• 7-10: Balanced smoothing (RECOMMENDED)\n• 11-15: Heavy smoothing, very clean signals\n\n💡 TIP: Higher values = cleaner but slower signals", group="📊 Signal Processing")
lookback = input.int(1000, "📊 Normalization Period", maxval=1000, minval=100, step=50, tooltip="📈 NORMALIZATION: Bars used for signal strength calculation\n• 500-700: Shorter-term normalization\n• 800-1000: Long-term normalization (RECOMMENDED)\n\n💡 TIP: More bars = more stable signal strength", group="📊 Signal Processing")
atrLength = input(8, "📏 Volatility Period (ATR)", tooltip="📊 VOLATILITY MEASUREMENT: Period for Average True Range calculation\n• 8-14: Responsive to recent volatility changes\n• 15-21: Balanced volatility measurement\n\n💡 TIP: Used for adaptive signal scaling", group="🌊 Wavelet Settings")

// Wavelet controls
useWavelet = input.bool(true, "Enable Magical Wavelet", tooltip="Turn ON for advanced wavelet analysis, OFF for simple EMA-based signals", group="🌊 Wavelet Settings")
noiseReduction = input.bool(true, "Enable Noise Reduction", tooltip="Reduces market noise for cleaner signals. Recommended: ON", group="🌊 Wavelet Settings")
wnMin = input(1, "NoiseR Min Length", tooltip="Minimum period for noise reduction (1-3). Lower = more aggressive filtering", group="🌊 Wavelet Settings")
wnMax = input(2, "NoiseR Max Length", tooltip="Maximum period for noise reduction (2-5). Higher = smoother but slower", group="🌊 Wavelet Settings")

// =============================================================================
// 💥 ADVANCED MULTI-TIMEFRAME S/R DETECTION INPUTS
// =============================================================================
enableBreakRetest = input.bool(true, "Enable Advanced S/R Analysis", tooltip="Advanced multi-timeframe support/resistance detection with superior accuracy!", group="💥 Advanced S/R Settings")
srPivotLength = input.int(15, "S/R Pivot Length", minval=3, maxval=50, tooltip="Pivot detection period (10-20 recommended). Higher = stronger levels but fewer signals", group="💥 Advanced S/R Settings")
srStrength = input.int(1, "Minimum S/R Strength", options=[1, 2, 3], tooltip="Minimum strength required for S/R levels (1=all, 2=medium, 3=strong only)", group="💥 Advanced S/R Settings")
srInvalidation = input.string("Close", "Break Detection Method", options=["Wick", "Close"], tooltip="How breaks are detected: Wick=any touch, Close=candle close", group="💥 Advanced S/R Settings")
expandZones = input.string("Only Valid", "Line Extension", options=["All", "Only Valid", "None"], tooltip="How S/R lines are extended", group="💥 Advanced S/R Settings")
showInvalidated = input.bool(true, "Show Broken Levels", tooltip="Display levels that have been broken (with different styling)", group="💥 Advanced S/R Settings")

// Multi-Timeframe Configuration
timeframe1Enabled = input.bool(true, "Current Timeframe", group="🕐 Multi-Timeframe S/R", inline="tf1", tooltip="Enable current timeframe S/R detection")
timeframe1 = input.timeframe("", "", group="� Multi-Timeframe S/R", inline="tf1")
timeframe2Enabled = input.bool(true, "Higher Timeframe 1", group="🕐 Multi-Timeframe S/R", inline="tf2", tooltip="Enable higher timeframe S/R for better accuracy")
timeframe2 = input.timeframe("D", "", group="� Multi-Timeframe S/R", inline="tf2")
timeframe3Enabled = input.bool(false, "Higher Timeframe 2", group="🕐 Multi-Timeframe S/R", inline="tf3", tooltip="Optional: Add another higher timeframe")
timeframe3 = input.timeframe("W", "", group="🕐 Multi-Timeframe S/R", inline="tf3")

// Enhanced Break/Retest Settings
showBreaks = input.bool(true, "Show Break Signals", group="💥 Advanced S/R Settings", inline="ShowBR", tooltip="Display break signals with neon styling")
showRetests = input.bool(true, "Show Retest Signals", group="💥 Advanced S/R Settings", inline="ShowBR", tooltip="Display retest signals with neon styling")
avoidFalseBreaks = input.bool(true, "Avoid False Breaks", group="💥 Advanced S/R Settings", tooltip="Use volume confirmation to avoid false breakouts")
breakVolumeThreshold = input.float(1.3, "Break Volume Threshold", minval=0.1, maxval=3.0, step=0.1, group="💥 Advanced S/R Settings", tooltip="Volume multiplier required for valid breaks (1.2-2.0 recommended)")
inverseBrokenLineColor = input.bool(true, "Inverse Color After Break", group="💥 Advanced S/R Settings", tooltip="Change line color after break (resistance becomes support, etc.)")

// =============================================================================
// 🤖 ADVANCED AI NEURAL NETWORK INPUTS
// =============================================================================
enableAI = input.bool(true, "🧠 Enable AI Prediction Engine", tooltip="🚀 ARTIFICIAL INTELLIGENCE: Advanced neural network system\n• Uses machine learning to adapt to market conditions\n• Learns from price patterns and market behavior\n• Significantly improves signal accuracy\n\n💡 HIGHLY RECOMMENDED: Keep this ON for best results!", group="🤖 AI Neural Network")
adaptivePeriod = input(50, "📊 AI Learning Period", tooltip="🎯 AI DATA PROCESSING: Period for neural network data normalization\n• 30-40: Fast adaptation, more volatile\n• 45-60: Balanced learning (RECOMMENDED)\n• 70-100: Slow adaptation, very stable\n\n💡 TIP: Higher values = more stable AI predictions", group="🤖 AI Neural Network")
adaptationRate = input.float(0.08, '⚡ Neural Learning Speed', step=0.01, maxval=0.15, tooltip="🧠 LEARNING RATE: How quickly the AI adapts to new market data\n• 0.03-0.06: Conservative learning, stable\n• 0.07-0.10: Balanced learning (RECOMMENDED)\n• 0.11-0.15: Aggressive learning, responsive\n\n💡 TIP: Higher = faster adaptation but more volatility", group="🤖 AI Neural Network")

// AI Feature Parameters - Advanced Market Analysis Components
momentum_period = input.int(29, "📈 Momentum Detector", tooltip="🎯 MOMENTUM ANALYSIS: RSI-based momentum detection period\n• 14-20: Very sensitive to momentum changes\n• 25-35: Balanced momentum detection (RECOMMENDED)\n• 40-50: Smooth momentum, strong signals only\n\n💡 TIP: Detects when market momentum is building", group="🧠 AI Features")
volatility_period = input.int(45, "🌊 Volatility Detector", tooltip="📊 VOLATILITY ANALYSIS: CCI-based volatility measurement period\n• 20-30: Sensitive to volatility spikes\n• 35-50: Balanced volatility detection (RECOMMENDED)\n• 60-100: Smooth volatility, major changes only\n\n💡 TIP: Helps AI understand market conditions", group="🧠 AI Features")
trend_strength_period = input.int(35, "💪 Trend Strength Analyzer", tooltip="🔥 TREND ANALYSIS: DMI-based trend strength measurement\n• 14-25: Responsive to trend changes\n• 30-40: Balanced trend analysis (RECOMMENDED)\n• 45-50: Strong trend confirmation only\n\n💡 TIP: Measures how strong the current trend is", group="🧠 AI Features")
oscillation_period = input.int(35, "🔄 Market Cycle Detector", tooltip="🌀 OSCILLATION ANALYSIS: Detects market cycles and turning points\n• 20-30: Sensitive to short-term cycles\n• 30-40: Balanced cycle detection (RECOMMENDED)\n• 45-50: Long-term cycle analysis\n\n💡 TIP: Helps predict market reversals", group="🧠 AI Features")
velocity_period = input(30, "⚡ Price Velocity Meter", tooltip="🚀 VELOCITY ANALYSIS: Measures the speed of price movements\n• 20-25: Sensitive to quick moves\n• 25-35: Balanced velocity measurement (RECOMMENDED)\n• 40-50: Smooth velocity, major moves only\n\n💡 TIP: Fast velocity = strong market moves", group="🧠 AI Features")
resistance_factor = input.float(3.2, "🛡️ Dynamic Resistance Factor", step=0.1, tooltip="🎯 RESISTANCE DETECTION: SuperTrend multiplier for resistance levels\n• 2.0-2.5: Sensitive resistance detection\n• 2.8-3.5: Balanced resistance (RECOMMENDED)\n• 4.0-5.0: Strong resistance levels only\n\n💡 TIP: Higher = stronger resistance confirmation", group="🧠 AI Features")
resistance_period = input.int(2, "🔍 Resistance Sensitivity", step=1, tooltip="⚡ RESISTANCE PERIOD: SuperTrend period for resistance detection\n• 1-2: Very sensitive (RECOMMENDED)\n• 3-4: Moderate sensitivity\n• 5: Conservative detection\n\n💡 TIP: Lower = more resistance levels detected", group="🧠 AI Features")

// =============================================================================
// 📈 ADDITIONAL TECHNICAL INDICATORS
// =============================================================================
enableMACD = input.bool(true, "Enable MACD Analysis", tooltip="Moving Average Convergence Divergence - excellent for trend changes and momentum", group="📈 Additional Indicators")
macdFast = input.int(12, "MACD Fast Length", tooltip="Fast EMA period (8-15 recommended). Standard MACD uses 12", group="📈 Additional Indicators")
macdSlow = input.int(26, "MACD Slow Length", tooltip="Slow EMA period (20-35 recommended). Standard MACD uses 26", group="📈 Additional Indicators")
macdSignal = input.int(9, "MACD Signal Length", tooltip="Signal line EMA period (7-12 recommended). Standard MACD uses 9", group="📈 Additional Indicators")

enableBollinger = input.bool(true, "Enable Bollinger Bands", tooltip="Bollinger Bands - great for volatility and mean reversion analysis", group="📈 Additional Indicators")
bbLength = input.int(20, "BB Length", tooltip="Bollinger Bands period (15-25 recommended). Standard BB uses 20", group="📈 Additional Indicators")
bbMult = input.float(2.0, "BB Multiplier", tooltip="Standard deviation multiplier (1.5-2.5). Higher = wider bands, fewer signals", group="📈 Additional Indicators")

enableStochastic = input.bool(true, "Enable Stochastic", tooltip="Stochastic Oscillator - identifies overbought/oversold conditions", group="📈 Additional Indicators")
stochK = input.int(14, "Stochastic %K", tooltip="Stochastic K period (10-21 recommended). Standard uses 14", group="📈 Additional Indicators")
stochD = input.int(3, "Stochastic %D", tooltip="Stochastic D smoothing (3-5 recommended). Higher = smoother signals", group="📈 Additional Indicators")

enableWilliamsR = input.bool(true, "Enable Williams %R", tooltip="Williams %R - similar to Stochastic but inverted scale", group="📈 Additional Indicators")
williamsLength = input.int(14, "Williams %R Length", tooltip="Williams %R period (10-21 recommended). Standard uses 14", group="📈 Additional Indicators")

enableFisherTransform = input.bool(true, "Enable Fisher Transform", tooltip="Fisher Transform - converts prices to Gaussian normal distribution for better signals", group="📈 Additional Indicators")
fisherLength = input.int(10, "Fisher Transform Length", tooltip="Fisher Transform period (8-15 recommended). Lower = more sensitive signals", group="📈 Additional Indicators")

// 🧠 Neural Network Weights (Advanced Users Only)
// These weights determine how much influence each indicator has on the final AI prediction
// Higher values = more influence. Recommended to keep default values unless you're experienced
alpha_momentum = 1      // RSI momentum weight (0.5-2.0 range)
beta_volatility = 4     // CCI volatility weight (2-6 range) - High influence
gamma_trend = 1         // DMI trend weight (0.5-2.0 range)
delta_oscillation = 2   // Oscillation weight (1-3 range)
epsilon_velocity = 5    // Price velocity weight (3-7 range) - High influence
zeta_resistance = 4     // SuperTrend resistance weight (2-6 range)

// Additional Technical Indicator Weights
theta_macd = 3          // MACD weight (2-5 range)
iota_bollinger = 2      // Bollinger Bands weight (1-4 range)
kappa_stochastic = 3    // Stochastic weight (2-5 range)
lambda_williams = 2     // Williams %R weight (1-4 range)
mu_fisher = 4           // Fisher Transform weight (2-6 range)

// Break Retest Analysis Weight
nu_break_retest = 6     // Break/Retest weight (4-8 range) - Highest influence

// 🌟 Neon Color Palette - Optimized for Dark Mode
upper_col = input.color(#00ff41, "Neon Green (Bullish)", inline = "neon_colors", group="🌟 Neon Display", tooltip="Main color for bullish/up signals. Bright neon green for high visibility")
lower_col = input.color(#ff0080, "Neon Pink (Bearish)", inline = "neon_colors", group="🌟 Neon Display", tooltip="Main color for bearish/down signals. Vibrant pink for clear distinction")
neutral_col = input.color(#ffff00, "Electric Yellow (Neutral)", inline = "neon_colors2", group="🌟 Neon Display", tooltip="Color for neutral/sideways market conditions")
break_col = input.color(#00ffff, "Cyan Glow (Breakouts)", inline = "neon_colors2", group="🌟 Neon Display", tooltip="Special color for breakout signals - highly important events!")
retest_col = input.color(#ff6600, "Orange Pulse (Retests)", inline = "neon_colors3", group="🌟 Neon Display", tooltip="Color for retest signals - when price returns to test broken levels")

// S/R Neon Colors
supportColor = input.color(#08ff8180, "Support Neon Glow", inline = "sr_colors", group="🌟 Neon Display", tooltip="Neon color for support levels - bright green glow")
resistanceColor = input.color(#ff364580, "Resistance Neon Glow", inline = "sr_colors", group="🌟 Neon Display", tooltip="Neon color for resistance levels - bright red glow")
breakColor = input.color(#00ffff, "Break Signal Glow", inline = "sr_colors2", group="🌟 Neon Display", tooltip="Special neon color for break signals")
textColor = input.color(#ffffff80, "Text Glow Color", inline = "sr_colors2", group="🌟 Neon Display", tooltip="Color for S/R level labels and text")

// Additional Neon Accent Colors
accent_col = input.color(#9d00ff, "Purple Accent", inline = "neon_colors3", group="🌟 Neon Display", tooltip="Accent color for special effects and highlights")
glow_col = input.color(#ffffff, "White Glow", inline = "neon_colors4", group="🌟 Neon Display", tooltip="White glow color for maximum contrast and visibility")
shadow_col = input.color(#1a1a1a, "Dark Shadow", inline = "neon_colors4", group="🌟 Neon Display", tooltip="Dark background color for panels and shadows")

// 🎨 Enhanced Neon Display Options
candleStyle = input.string("Neon Glow", "Candle Style", options=["Original", "Enhanced", "Gradient", "Neon Glow", "Electric"],
           tooltip="Choose your preferred visual style:\n• Original: Classic colors\n• Enhanced: Improved visibility\n• Gradient: Smooth color transitions\n• Neon Glow: Full neon effect (recommended!)\n• Electric: High-intensity pulsing",
           group="🌟 Neon Display")
showMomentum = input.bool(true, "Show Momentum Pulse", tooltip="Shows momentum as pulsing histogram bars below main signal", group="🌟 Neon Display")
showBreakRetest = input.bool(true, "Show S/R Neon Lines", tooltip="Displays special neon lines for support/resistance levels and break/retest signals", group="🌟 Neon Display")
glowIntensity = input.float(0.8, "Neon Glow Intensity", minval=0.1, maxval=1.0, step=0.1,
              tooltip="Controls overall glow brightness (0.1-1.0):\n• 0.1-0.3: Subtle glow\n• 0.4-0.7: Moderate glow\n• 0.8-1.0: Maximum neon effect",
              group="🌟 Neon Display")
pulseEffect = input.bool(true, "Enable Pulse Effect", tooltip="Adds animated pulsing to break/retest signals and zero line. Creates dynamic visual effects!", group="🌟 Neon Display")

// S/R Visual Style Settings
styleMode = input.string("Lines", "S/R Display Style", options=["Lines", "Zones"], group="🌟 Neon Display", tooltip="Choose between neon lines or glowing zones for S/R levels")
lineStyle = input.string("____", "S/R Line Style", options=["____", "----", "...."], group="🌟 Neon Display", tooltip="Visual style of S/R lines")
lineWidth = input.int(3, "S/R Line Width", minval=1, maxval=5, group="🌟 Neon Display", tooltip="Thickness of S/R lines (recommended: 2-4 for neon effect)")
zoneSize = input.float(1.5, "Zone Glow Width", minval=0.1, maxval=5.0, step=0.1, group="🌟 Neon Display", tooltip="Width of glowing zones around S/R levels")

// =============================================================================
// 📏 NEON REFERENCE GRID SETTINGS
// =============================================================================
showHLines = input.bool(true, "Show Neon Grid Lines", tooltip="Displays horizontal reference lines with signal strength labels. Helps identify key levels!", group="🌟 Neon Display")
hlineColor = input.color(color.new(#00ffff, 60), "Grid Glow Color", tooltip="Base color for the reference grid lines", group="🌟 Neon Display")
hlineStyle = input.string("Dashed", "Grid Line Style", options=["Solid", "Dashed", "Dotted"], tooltip="Visual style of grid lines. Dashed is recommended for better chart readability", group="🌟 Neon Display")
gridGlow = input.bool(true, "Enable Grid Glow Effect", tooltip="Adds glow effect to grid lines for enhanced neon appearance", group="🌟 Neon Display")

// =============================================================================
// ADVANCED MULTI-TIMEFRAME S/R DETECTION SYSTEM
// =============================================================================

// Constants for advanced S/R system
const int maxSRInfoListSize = 15
const int maxBarInfoListSize = 3000
const int maxDistanceToLastBar = 500
const int minSRSize = 5
const int retestLabelCooldown = 3
const float tooCloseATR = 1.0 / 8.0
const int labelOffsetBars = 20
const int atrLen = 20

// Enhanced ATR and volume calculations
atr = ta.atr(atrLen)
avgVolume = ta.sma(volume, atrLen)
zoneSizeATR = zoneSize * 0.075

// Current timeframe tracking
var int curTFMS = timeframe.in_seconds(timeframe.period) * 1000
var map<string, bool> alerts = map.new<string, bool>()
alerts.put("Retest", false)
alerts.put("Break", false)

// Advanced S/R Information Structure
type srInfo
    int startTime
    float price
    string srType
    int strength
    string timeframeStr
    bool ephemeral = false
    int breakTime
    array<int> retestTimes

// Enhanced S/R Object with Neon Rendering
type srObj
    srInfo info
    bool startFixed
    bool breakFixed
    bool rendered
    string combinedTimeframeStr
    line srLine
    box srBox
    label srLabel
    label breakLabel
    array<label> retestLabels

// Global S/R tracking arrays
var allSRList = array.new<srObj>()
var allSRInfoList = array.new<srInfo>()

// Bar information tracking for precise timing
type barInfo
    int t
    int tc
    float c
    float h
    float l

var barInfoList = array.new<barInfo>()

// =============================================================================
// ADVANCED S/R DETECTION FUNCTIONS
// =============================================================================

// Enhanced timeframe formatting for neon display
formatTimeframeString(string formatTimeframe, bool short = false) =>
    timeframeF = (formatTimeframe == "" ? timeframe.period : formatTimeframe)
    if str.contains(timeframeF, "D") or str.contains(timeframeF, "W") or str.contains(timeframeF, "S") or str.contains(timeframeF, "M")
        timeframe.from_seconds(timeframe.in_seconds(timeframeF))
    else
        seconds = timeframe.in_seconds(timeframeF)
        if seconds >= 3600
            hourCount = int(seconds / 3600)
            if short
                str.tostring(hourCount) + "h"
            else
                str.tostring(hourCount) + " Hour" + (hourCount > 1 ? "s" : "")
        else
            if short
                timeframeF + "m"
            else
                timeframeF + " Min"

// Advanced value finder for precise timing
findValRtnTime(barInfo[] biList, valToFind, toSearch, searchMode, minTime, maxTime, int defVal = na) =>
    int rtnTime = defVal
    float minDiff = na
    if biList.size() > 0
        for i = biList.size() - 1 to 0
            curBI = biList.get(i)
            if curBI.t >= minTime and curBI.t < maxTime
                toLook = (toSearch == "Low" ? curBI.l : toSearch == "High" ? curBI.h : curBI.c)
                if searchMode == "Nearest"
                    curDiff = math.abs(valToFind - toLook)
                    if na(minDiff)
                        rtnTime := curBI.t
                        minDiff := curDiff
                    else
                        if curDiff <= minDiff
                            minDiff := curDiff
                            rtnTime := curBI.t
                if searchMode == "Higher"
                    if toLook >= valToFind
                        rtnTime := curBI.t
                        break
                if searchMode == "Lower"
                    if toLook <= valToFind
                        rtnTime := curBI.t
                        break
    rtnTime

// Enhanced S/R object finder
getSR(srObj[] list, srPrice, eph, srType, timeframeStr) =>
    srObj rtnSR = na
    if list.size() > 0
        for i = 0 to list.size() - 1
            curSR = list.get(i)
            if curSR.info.price == srPrice and curSR.info.ephemeral == eph and curSR.info.srType == srType and curSR.info.timeframeStr == timeframeStr
                rtnSR := curSR
                break
    rtnSR

// =============================================================================
// ENHANCED S/R CALCULATIONS WITH MULTI-TIMEFRAME SUPPORT
// =============================================================================

// Enhanced pivot detection for current timeframe
pivotHigh = ta.pivothigh(srPivotLength, srPivotLength)
pivotLow = ta.pivotlow(srPivotLength, srPivotLength)

// Bar information tracking for precise timing
insideBounds = (bar_index > last_bar_index - maxDistanceToLastBar)
barInfoList.unshift(barInfo.new(time, time_close, close, high, low))
if barInfoList.size() > maxBarInfoListSize
    barInfoList.pop()

// Current timeframe S/R detection
if insideBounds and barstate.isconfirmed
    // Find Support levels with enhanced accuracy
    if not na(pivotLow)
        validSR = true
        if allSRInfoList.size() > 0
            for i = 0 to allSRInfoList.size() - 1
                curRSInfo = allSRInfoList.get(i)
                if (math.abs(curRSInfo.price - pivotLow) < atr * tooCloseATR) and na(curRSInfo.breakTime)
                    validSR := false
                    break

        if validSR
            newSRInfo = srInfo.new(barInfoList.get(srPivotLength).t, pivotLow, "Support", 1, timeframe.period)
            newSRInfo.retestTimes := array.new<int>()
            allSRInfoList.unshift(newSRInfo)
            while allSRInfoList.size() > maxSRInfoListSize
                allSRInfoList.pop()

    // Find Resistance levels with enhanced accuracy
    if not na(pivotHigh)
        validSR = true
        if allSRInfoList.size() > 0
            for i = 0 to allSRInfoList.size() - 1
                curRSInfo = allSRInfoList.get(i)
                if (math.abs(curRSInfo.price - pivotHigh) < atr * tooCloseATR) and na(curRSInfo.breakTime)
                    validSR := false
                    break
        if validSR
            newSRInfo = srInfo.new(barInfoList.get(srPivotLength).t, pivotHigh, "Resistance", 1, timeframe.period)
            newSRInfo.retestTimes := array.new<int>()
            allSRInfoList.unshift(newSRInfo)
            if allSRInfoList.size() > maxSRInfoListSize
                allSRInfoList.pop()

// Enhanced break and retest detection with volume confirmation
if insideBounds and (srInvalidation == "Wick" or barstate.isconfirmed)
    if allSRInfoList.size() > 0
        for i = 0 to allSRInfoList.size() - 1
            srInfo curSRInfo = allSRInfoList.get(i)

            // Enhanced break detection with volume confirmation
            invHigh = (srInvalidation == "Close" ? close : high)
            invLow = (srInvalidation == "Close" ? close : low)
            closeTime = time
            if na(curSRInfo.breakTime)
                if curSRInfo.srType == "Resistance" and invHigh > curSRInfo.price
                    if (not avoidFalseBreaks) or (volume > avgVolume * breakVolumeThreshold)
                        curSRInfo.breakTime := closeTime
                        if inverseBrokenLineColor and (not curSRInfo.ephemeral) and curSRInfo.strength >= srStrength
                            ephSR = srInfo.new(closeTime, curSRInfo.price, "Support", curSRInfo.strength, curSRInfo.timeframeStr, true)
                            ephSR.retestTimes := array.new<int>()
                            allSRInfoList.unshift(ephSR)
                else if curSRInfo.srType == "Support" and invLow < curSRInfo.price
                    if (not avoidFalseBreaks) or (volume > avgVolume * breakVolumeThreshold)
                        curSRInfo.breakTime := closeTime
                        if inverseBrokenLineColor and (not curSRInfo.ephemeral) and curSRInfo.strength >= srStrength
                            ephSR = srInfo.new(closeTime, curSRInfo.price, "Resistance", curSRInfo.strength, curSRInfo.timeframeStr, true)
                            ephSR.retestTimes := array.new<int>()
                            allSRInfoList.unshift(ephSR)

            // Enhanced strength calculation and retest detection
            if na(curSRInfo.breakTime) and time > curSRInfo.startTime and barstate.isconfirmed
                if curSRInfo.srType == "Resistance" and high >= curSRInfo.price and close <= curSRInfo.price
                    int lastRetestTime = 0
                    if curSRInfo.retestTimes.size() > 0
                        lastRetestTime := curSRInfo.retestTimes.get(0)

                    if lastRetestTime != time
                        if not curSRInfo.ephemeral
                            curSRInfo.strength += 1
                        curSRInfo.retestTimes.unshift(time)

                else if curSRInfo.srType == "Support" and low <= curSRInfo.price and close >= curSRInfo.price
                    int lastRetestTime = 0
                    if curSRInfo.retestTimes.size() > 0
                        lastRetestTime := curSRInfo.retestTimes.get(0)

                    if lastRetestTime != time
                        if not curSRInfo.ephemeral
                            curSRInfo.strength += 1
                        curSRInfo.retestTimes.unshift(time)

// Advanced S/R timeframe fixing for precise rendering
fixSRToTimeframe(srObj sr) =>
    srMS = math.max(timeframe.in_seconds(sr.info.timeframeStr), timeframe.in_seconds()) * 1000
    if (not sr.startFixed)
        if not sr.info.ephemeral
            if sr.info.srType == "Resistance"
                sr.info.startTime := findValRtnTime(barInfoList, sr.info.price, "High", "Nearest", sr.info.startTime - srMS, sr.info.startTime + srMS, sr.info.startTime)
            else
                sr.info.startTime := findValRtnTime(barInfoList, sr.info.price, "Low", "Nearest", sr.info.startTime - srMS, sr.info.startTime + srMS, sr.info.startTime)
            sr.startFixed := true
        else
            if allSRList.size() > 0
                for i = 0 to allSRList.size() - 1
                    curSR = allSRList.get(i)
                    if (not curSR.info.ephemeral) and (not na(curSR.info.breakTime)) and curSR.info.price == sr.info.price and ((sr.info.srType == "Resistance" and curSR.info.srType == "Support") or (sr.info.srType == "Support" and curSR.info.srType == "Resistance"))
                        if curSR.breakFixed
                            sr.info.startTime := curSR.info.breakTime
                            sr.startFixed := true
                        break

    if not na(sr.info.breakTime)
        if (not sr.breakFixed)
            if sr.info.srType == "Resistance"
                sr.info.breakTime := findValRtnTime(barInfoList, sr.info.price, srInvalidation == "Wick" ? "High" : "Close", "Higher", sr.info.breakTime - srMS, sr.info.breakTime + srMS, sr.info.breakTime)
            else
                sr.info.breakTime := findValRtnTime(barInfoList, sr.info.price, srInvalidation == "Wick" ? "Low" : "Close", "Lower", sr.info.breakTime - srMS, sr.info.breakTime + srMS, sr.info.breakTime)
            sr.breakFixed := true

// Enhanced neon S/R rendering with glow effects
renderSRObj(srObj sr) =>
    if na(sr.info.breakTime) or showInvalidated
        sr.rendered := true
        endTime = nz(sr.info.breakTime, time + curTFMS * labelOffsetBars)
        extendType = extend.none
        if na(sr.info.breakTime)
            extendType := extend.right
        if expandZones == "Only Valid" and na(sr.info.breakTime)
            extendType := extend.both
        else if expandZones == "All"
            extendType := extend.both
            endTime := time + curTFMS * labelOffsetBars

        labelTitle = formatTimeframeString(sr.info.timeframeStr)
        if not na(sr.combinedTimeframeStr)
            labelTitle := sr.combinedTimeframeStr

        labelTitle += " | " + str.tostring(sr.info.price, format.mintick) + " 💪" + str.tostring(sr.info.strength)

        // Enhanced neon colors based on break status and strength
        srColor = sr.info.srType == "Resistance" ? resistanceColor : supportColor
        if not na(sr.info.breakTime) and inverseBrokenLineColor
            srColor := sr.info.srType == "Resistance" ? supportColor : resistanceColor

        // Apply neon glow effect
        neonGlow = math.round(100 - (glowIntensity * 30))
        glowColor = color.new(srColor, neonGlow)

        if styleMode == "Lines"
            // Enhanced neon line with glow
            sr.srLine := line.new(sr.info.startTime, sr.info.price, endTime, sr.info.price, xloc = xloc.bar_time, color = glowColor, width = lineWidth, style = lineStyle == "----" ? line.style_dashed : lineStyle == "...." ? line.style_dotted : line.style_solid, extend = extendType)
            // Neon label
            sr.srLabel := label.new(extendType == extend.none ? ((sr.info.startTime + endTime) / 2) : endTime, sr.info.price, xloc = xloc.bar_time, text = labelTitle, textcolor = textColor, style = label.style_none)
        else
            // Enhanced neon zone with glow
            sr.srBox := box.new(sr.info.startTime, sr.info.price + atr * zoneSizeATR, endTime, sr.info.price - atr * zoneSizeATR, xloc = xloc.bar_time, bgcolor = glowColor, border_color = na, text = labelTitle, text_color = textColor, extend = extendType, text_size = size.normal, text_halign = (extendType != extend.none) ? text.align_right : text.align_center)

        // Enhanced neon break labels
        if showBreaks
            if not na(sr.info.breakTime)
                breakPulse = pulseEffect ? math.abs(math.sin(bar_index * 0.15)) * 30 : 20
                sr.breakLabel := label.new(sr.info.breakTime, sr.info.price, "💥", yloc = sr.info.srType == "Resistance" ? yloc.belowbar : yloc.abovebar, style = sr.info.srType == "Resistance" ? label.style_label_up : label.style_label_down, color = color.new(breakColor, breakPulse), textcolor = color.new(textColor, 0), xloc = xloc.bar_time, size = size.small)
                if (time - curTFMS <= sr.info.breakTime) and (time + curTFMS >= sr.info.breakTime)
                    alerts.put("Break", true)

        // Enhanced neon retest labels
        if showRetests
            if sr.info.retestTimes.size() > 0
                for i = sr.info.retestTimes.size() - 1 to 0
                    curRetestTime = sr.info.retestTimes.get(i)
                    cooldownOK = true
                    if sr.retestLabels.size() > 0
                        lastLabel = sr.retestLabels.get(0)
                        if math.abs(lastLabel.get_x() - curRetestTime) < curTFMS * retestLabelCooldown
                            cooldownOK := false

                    if cooldownOK and (curRetestTime >= sr.info.startTime) and (na(sr.info.breakTime) or curRetestTime < sr.info.breakTime)
                        if time - curTFMS <= curRetestTime and time >= curRetestTime
                            alerts.put("Retest", true)
                        retestPulse = pulseEffect ? math.abs(math.cos(bar_index * 0.12)) * 25 : 15
                        sr.retestLabels.unshift(label.new(curRetestTime, sr.info.price, "🔄", yloc = sr.info.srType == "Resistance" ? yloc.abovebar : yloc.belowbar, style = sr.info.srType == "Resistance" ? label.style_label_down : label.style_label_up, color = color.new(sr.info.srType == "Resistance" ? resistanceColor : supportColor, retestPulse), textcolor = color.new(textColor, 0), xloc = xloc.bar_time, size = size.small))

// Safe S/R object deletion - Modified to avoid security() context issues
safeDeleteSRObj(srObj sr) =>
    // Only mark as not rendered, actual deletion will happen in main context
    sr.rendered := false

// Enhanced safe deletion function for main context only
safeDeleteSRObjMain(srObj sr) =>
    // Mark objects for deletion - actual deletion happens outside security() context
    sr.rendered := false
    // Set drawing objects to na to mark them for cleanup
    sr.srLine := na
    sr.srBox := na
    sr.srLabel := na
    sr.breakLabel := na
    if not na(sr.retestLabels)
        sr.retestLabels.clear()

// Safe deletion function that can be used in security() context
safeDeleteSRObjInSecurity(srObj sr) =>
    // Only mark as not rendered when inside security() context
    // Drawing object deletion must happen in main context
    sr.rendered := false

// Cleanup function to actually delete drawing objects (main context only)
cleanupDrawingObjects(srObj sr) =>
    // This function should only be called in main context, never inside security()
    if not na(sr.srLine)
        line.delete(sr.srLine)
        sr.srLine := na
    if not na(sr.srBox)
        box.delete(sr.srBox)
        sr.srBox := na
    if not na(sr.srLabel)
        label.delete(sr.srLabel)
        sr.srLabel := na
    if not na(sr.breakLabel)
        label.delete(sr.breakLabel)
        sr.breakLabel := na
    if not na(sr.retestLabels) and sr.retestLabels.size() > 0
        for i = 0 to sr.retestLabels.size() - 1
            curRetestLabel = sr.retestLabels.get(i)
            if not na(curRetestLabel)
                label.delete(curRetestLabel)
        sr.retestLabels.clear()

// Multi-timeframe S/R handler with enhanced accuracy
handleTF(tfStr, tfEnabled) =>
    if tfEnabled
        tfSRInfoList = request.security(syminfo.tickerid, tfStr, allSRInfoList)
        if not na(tfSRInfoList) and tfSRInfoList.size() > 0
            for i = 0 to tfSRInfoList.size() - 1
                srInfo curSRInfo = tfSRInfoList.get(i)
                currentSameSR = getSR(allSRList, curSRInfo.price, curSRInfo.ephemeral, curSRInfo.srType, curSRInfo.timeframeStr)
                if not na(currentSameSR)
                    if currentSameSR.startFixed
                        curSRInfo.startTime := currentSameSR.info.startTime
                    if currentSameSR.breakFixed
                        curSRInfo.breakTime := currentSameSR.info.breakTime
                    curSRInfo.retestTimes := currentSameSR.info.retestTimes
                    currentSameSR.info := curSRInfo
                    if not currentSameSR.breakFixed
                        fixSRToTimeframe(currentSameSR)
                else
                    srObj newSRObj = srObj.new(curSRInfo)
                    newSRObj.info.retestTimes := array.new<int>()
                    newSRObj.retestLabels := array.new<label>()
                    fixSRToTimeframe(newSRObj)
                    allSRList.unshift(newSRObj)
    true

// Main S/R processing and rendering
if (bar_index > last_bar_index - maxDistanceToLastBar * 8) and barstate.isconfirmed
    if allSRList.size() > 0
        for i = 0 to allSRList.size() - 1
            srObj curSRObj = allSRList.get(i)
            cleanupDrawingObjects(curSRObj)
    allSRList.clear()

    handleTF(timeframe1, timeframe1Enabled)
    handleTF(timeframe2, timeframe2Enabled)
    handleTF(timeframe3, timeframe3Enabled)

    if allSRList.size() > 0
        for i = 0 to allSRList.size() - 1
            srObj curSRObj = allSRList.get(i)
            safeDeleteSRObjMain(curSRObj)
            tooClose = false
            for j = 0 to allSRList.size() - 1
                closeSR = allSRList.get(j)
                if closeSR.rendered and math.abs(closeSR.info.price - curSRObj.info.price) <= tooCloseATR * atr and closeSR.info.srType == curSRObj.info.srType and closeSR.info.ephemeral == curSRObj.info.ephemeral
                    tooClose := true
                    if not str.contains((na(closeSR.combinedTimeframeStr) ? formatTimeframeString(closeSR.info.timeframeStr) : closeSR.combinedTimeframeStr), formatTimeframeString(curSRObj.info.timeframeStr))
                        if na(closeSR.combinedTimeframeStr)
                            closeSR.combinedTimeframeStr := formatTimeframeString(closeSR.info.timeframeStr) + " & " + formatTimeframeString(curSRObj.info.timeframeStr)
                        else
                            closeSR.combinedTimeframeStr += " & " + formatTimeframeString(curSRObj.info.timeframeStr)
                    break

            if (curSRObj.info.strength >= srStrength) and (na(curSRObj.info.breakTime) or (curSRObj.info.breakTime - curSRObj.info.startTime) >= minSRSize * curTFMS) and (not tooClose)
                renderSRObj(curSRObj)

// Current timeframe retest detection
if allSRList.size() > 0 and barstate.isconfirmed
    for i = 0 to allSRList.size() - 1
        srObj curSR = allSRList.get(i)
        if na(curSR.info.breakTime) and time > curSR.info.startTime
            if curSR.info.srType == "Resistance" and high >= curSR.info.price and close <= curSR.info.price
                int lastRetestTime = 0
                if curSR.info.retestTimes.size() > 0
                    lastRetestTime := curSR.info.retestTimes.get(0)

                if lastRetestTime != time
                    curSR.info.retestTimes.unshift(time)

            else if curSR.info.srType == "Support" and low <= curSR.info.price and close >= curSR.info.price
                int lastRetestTime = 0
                if curSR.info.retestTimes.size() > 0
                    lastRetestTime := curSR.info.retestTimes.get(0)

                if lastRetestTime != time
                    curSR.info.retestTimes.unshift(time)

// Enhanced break/retest signal generation
br_signal = 0.0
resistance_break_signal = false
support_break_signal = false
resistance_retest_signal = false
support_retest_signal = false

if enableBreakRetest and allSRList.size() > 0
    for i = 0 to allSRList.size() - 1
        srObj curSR = allSRList.get(i)
        if not na(curSR.info.breakTime)
            if (time - curTFMS <= curSR.info.breakTime) and (time + curTFMS >= curSR.info.breakTime)
                if curSR.info.srType == "Resistance"
                    resistance_break_signal := true
                    br_signal := math.max(br_signal, 0.8 * (curSR.info.strength / 3.0))
                else
                    support_break_signal := true
                    br_signal := math.min(br_signal, -0.8 * (curSR.info.strength / 3.0))

        if curSR.info.retestTimes.size() > 0
            lastRetestTime = curSR.info.retestTimes.get(0)
            if (time - curTFMS <= lastRetestTime) and (time >= lastRetestTime)
                if curSR.info.srType == "Resistance"
                    resistance_retest_signal := true
                    br_signal := math.max(br_signal, 0.6 * (curSR.info.strength / 3.0))
                else
                    support_retest_signal := true
                    br_signal := math.min(br_signal, -0.6 * (curSR.info.strength / 3.0))

// =============================================================================
// ENHANCED S/R LEVEL TRACKING FOR INFO PANEL AND ALERTS
// =============================================================================

// Variables for tracking recent resistance and support levels
var float recent_resistance = na
var int resistance_level_strength = 0
var float recent_support = na
var int support_level_strength = 0
var bool resistance_broken = false
var bool resistance_retested = false
var bool support_broken = false
var bool support_retested = false

// Enhanced strong break detection with volume confirmation
strong_resistance_break = false
strong_support_break = false
weak_break_warning = false

// Update recent resistance and support levels from S/R list
if enableBreakRetest and allSRList.size() > 0
    float closest_resistance = na
    float closest_support = na
    int res_strength = 0
    int sup_strength = 0

    // Find closest resistance and support levels
    for i = 0 to allSRList.size() - 1
        srObj curSR = allSRList.get(i)
        if na(curSR.info.breakTime) and curSR.info.strength >= srStrength
            if curSR.info.srType == "Resistance" and curSR.info.price > close
                if na(closest_resistance) or curSR.info.price < closest_resistance
                    closest_resistance := curSR.info.price
                    res_strength := curSR.info.strength
            else if curSR.info.srType == "Support" and curSR.info.price < close
                if na(closest_support) or curSR.info.price > closest_support
                    closest_support := curSR.info.price
                    sup_strength := curSR.info.strength

    // Update tracking variables
    if not na(closest_resistance)
        recent_resistance := closest_resistance
        resistance_level_strength := res_strength
    if not na(closest_support)
        recent_support := closest_support
        support_level_strength := sup_strength

// Enhanced break detection with volume confirmation for strong breaks
if enableBreakRetest and allSRList.size() > 0
    for i = 0 to allSRList.size() - 1
        srObj curSR = allSRList.get(i)

        // Check for strong breaks with high volume
        if not na(curSR.info.breakTime)
            if (time - curTFMS <= curSR.info.breakTime) and (time + curTFMS >= curSR.info.breakTime)
                volumeConfirmed = volume > avgVolume * breakVolumeThreshold
                strongLevel = curSR.info.strength >= 3

                if curSR.info.srType == "Resistance"
                    resistance_broken := true
                    if volumeConfirmed and strongLevel
                        strong_resistance_break := true
                    else if not volumeConfirmed
                        weak_break_warning := true
                else
                    support_broken := true
                    if volumeConfirmed and strongLevel
                        strong_support_break := true
                    else if not volumeConfirmed
                        weak_break_warning := true

        // Check for retests
        if curSR.info.retestTimes.size() > 0
            lastRetestTime = curSR.info.retestTimes.get(0)
            if (time - curTFMS <= lastRetestTime) and (time >= lastRetestTime)
                if curSR.info.srType == "Resistance"
                    resistance_retested := true
                else
                    support_retested := true

// =============================================================================
// WAVELET FUNCTIONS & CALCULATIONS
// =============================================================================

// Wavelet decomposition
pi = 3.14159265359
wavelet(src, len) =>
    alpha = (1 - math.sin(2 * pi / len)) / math.cos(2 * pi / len)
    hp = 0.0
    hp := (1 - alpha/2) * (src - src[1]) + (1 - alpha) * nz(hp[1])
    ta.ema(hp, 3)

// Multi-scale decomposition with enhanced sensitivity
fastComponent = useWavelet ? wavelet(close, shortLength) : ta.ema(close, shortLength)
slowComponent = useWavelet ? wavelet(close, longLength) : ta.ema(close, longLength)
scaleRatio = longLength / shortLength
rawSignal = (fastComponent - slowComponent) * scaleRatio

// Noise reduction
wn(src) =>
    maFast = ta.ema(src, wnMin)
    maSlow = ta.ema(src, wnMax)
    noiseReduction ? (maFast + maSlow) / 2 : src

smoothedSignal = wn(rawSignal)

// Normalization with scale preservation
atrAdjustedSignal = smoothedSignal / math.max(atr * scaleRatio, 0.001)
absMax = ta.highest(math.abs(atrAdjustedSignal), lookback)
waveletNormalized = atrAdjustedSignal / math.max(absMax, 0.001)
waveletNormalized := math.min(math.max(waveletNormalized, -1), 1)

// =============================================================================
// ADDITIONAL INDICATORS CALCULATIONS
// =============================================================================

// MACD Analysis
[macdLine, signalLine, histLine] = ta.macd(close, macdFast, macdSlow, macdSignal)
macdNormalized = enableMACD ? ta.stoch(macdLine, macdLine, macdLine, 50) / 100 - 0.5 : 0

// Bollinger Bands Analysis
basis = ta.sma(close, bbLength)
dev = bbMult * ta.stdev(close, bbLength)
upper_bb = basis + dev
lower_bb = basis - dev
bb_position = enableBollinger ? (close - lower_bb) / (upper_bb - lower_bb) - 0.5 : 0

// Stochastic Analysis
k = ta.stoch(close, high, low, stochK)
d = ta.sma(k, stochD)
stochNormalized = enableStochastic ? (k / 100) - 0.5 : 0

// Williams %R Analysis
williams_r = ta.wpr(williamsLength)
williamsNormalized = enableWilliamsR ? (williams_r + 50) / 100 - 0.5 : 0

// Fisher Transform Analysis
fisher_transform = 0.0
if enableFisherTransform
    price_normalized = 0.33 * 2 * ((close - ta.lowest(close, fisherLength)) / (ta.highest(close, fisherLength) - ta.lowest(close, fisherLength)) - 0.5)
    smooth_price = ta.ema(price_normalized, 5)
    fisher_transform := 0.5 * math.log((1 + smooth_price) / (1 - smooth_price))
    fisher_transform := ta.ema(fisher_transform, 3) / 3

// =============================================================================
// ADAPTIVE NEURAL NETWORK FUNCTIONS & CALCULATIONS
// =============================================================================

// Adaptive Data Standardization Engine
standardize_data(source_data) =>
    mean_baseline = ta.sma(source_data, adaptivePeriod)
    deviation = ta.stdev(source_data, adaptivePeriod)
    standardized = (source_data - mean_baseline) / math.max(deviation, 0.0001)
    standardized

// Enhanced Neural Activation Function
neural_activation_enhanced(f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, bias, w1, w2, w3, w4, w5, w6, w7, w8, w9, w10, w11, w12) =>
    neural_input = bias + w1 * f1 + w2 * f2 + w3 * f3 + w4 * f4 + w5 * f5 + w6 * f6 + w7 * f7 + w8 * f8 + w9 * f9 + w10 * f10 + w11 * f11 + w12 * f12
    activation_output = 1 / (1 + math.exp(-neural_input))
    activation_output

// Prediction Error Calculator
prediction_error(actual_target, predicted_output) =>
    safe_predicted = math.max(math.min(predicted_output, 0.9999), 0.0001)
    error = -actual_target * math.log(safe_predicted) - (1 - actual_target) * math.log(1 - safe_predicted)
    error

// Advanced Feature Engineering Module
momentum_detector = ta.rsi(close, momentum_period)
volatility_detector = ta.cci(hlc3, volatility_period)
[trend_positive, trend_negative, _] = ta.dmi(trend_strength_period, 10)

// Custom Oscillation Detector 
highest_position(length) =>
    bars_since_high = ta.highestbars(high, length)
    oscillation_up = ((length + bars_since_high) / length) * 100
    oscillation_up

lowest_position(length) =>
    bars_since_low = ta.lowestbars(low, length)
    oscillation_down = ((length + bars_since_low) / length) * 100
    oscillation_down

oscillation_up = highest_position(oscillation_period)
oscillation_down = lowest_position(oscillation_period)

// Price Velocity Analyzer
velocity_fast = ta.ema(close, velocity_period)
velocity_slow = ta.ema(close, velocity_period - 10)

// Dynamic Resistance Detection System
[resistance_level, resistance_direction] = ta.supertrend(resistance_factor, resistance_period)

// Enhanced Neural Network Weight Management System
var float bias_node = 1.0
var float learning_adj_momentum = 0.0
var float learning_adj_volatility = 0.0
var float learning_adj_trend = 0.0
var float learning_adj_oscillation = 0.0
var float learning_adj_velocity = 0.0
var float learning_adj_resistance = 0.0
var float learning_adj_macd = 0.0
var float learning_adj_bollinger = 0.0
var float learning_adj_stochastic = 0.0
var float learning_adj_williams = 0.0
var float learning_adj_fisher = 0.0
var float learning_adj_break_retest = 0.0

// Target Variable Generation 
market_direction = standardize_data(close) > 0 ? 1 : 0

// Enhanced Feature Vector Construction 
feature_momentum = momentum_detector > 50 ? 1 : 0
feature_volatility = 0.5
if ta.crossover(volatility_detector, 100)
    feature_volatility := 1
if ta.crossunder(volatility_detector, -100)
    feature_volatility := 0

feature_trend = trend_positive > trend_negative ? 1 : 0
feature_oscillation = oscillation_up > oscillation_down ? 1 : 0
feature_velocity = velocity_fast > velocity_slow ? 1 : 0
feature_resistance = resistance_direction == -1 ? 1 : 0

// Additional features
feature_macd = macdLine > signalLine ? 1 : 0
feature_bollinger = close > basis ? 1 : 0
feature_stochastic = k > d ? 1 : 0
feature_williams = williams_r > -50 ? 1 : 0
feature_fisher = fisher_transform > 0 ? 1 : 0

// Break Retest features
feature_break_retest = br_signal > 0 ? 1 : 0

// Enhanced Adaptive Neural Network Processing
ai_prediction = 0.0
if enableAI
    current_momentum = alpha_momentum + learning_adj_momentum
    current_volatility = beta_volatility + learning_adj_volatility
    current_trend = gamma_trend + learning_adj_trend
    current_oscillation = delta_oscillation + learning_adj_oscillation
    current_velocity = epsilon_velocity + learning_adj_velocity
    current_resistance = zeta_resistance + learning_adj_resistance
    current_macd = theta_macd + learning_adj_macd
    current_bollinger = iota_bollinger + learning_adj_bollinger
    current_stochastic = kappa_stochastic + learning_adj_stochastic
    current_williams = lambda_williams + learning_adj_williams
    current_fisher = mu_fisher + learning_adj_fisher
    current_break_retest = nu_break_retest + learning_adj_break_retest
    
    // Forward Pass: Generate Initial Prediction with Enhanced Features
    initial_prediction = neural_activation_enhanced(feature_momentum, feature_volatility, feature_trend, feature_oscillation, feature_velocity, feature_resistance, feature_macd, feature_bollinger, feature_stochastic, feature_williams, feature_fisher, feature_break_retest, bias_node, current_momentum, current_volatility, current_trend, current_oscillation, current_velocity, current_resistance, current_macd, current_bollinger, current_stochastic, current_williams, current_fisher, current_break_retest)
    
    // Backward Pass: Enhanced Adaptive Weight Adjustment 
    prediction_gradient = initial_prediction - market_direction
    learning_adj_momentum := learning_adj_momentum - adaptationRate * prediction_gradient * feature_momentum
    learning_adj_volatility := learning_adj_volatility - adaptationRate * prediction_gradient * feature_volatility
    learning_adj_trend := learning_adj_trend - adaptationRate * prediction_gradient * feature_trend
    learning_adj_oscillation := learning_adj_oscillation - adaptationRate * prediction_gradient * feature_oscillation
    learning_adj_velocity := learning_adj_velocity - adaptationRate * prediction_gradient * feature_velocity
    learning_adj_resistance := learning_adj_resistance - adaptationRate * prediction_gradient * feature_resistance
    learning_adj_macd := learning_adj_macd - adaptationRate * prediction_gradient * feature_macd
    learning_adj_bollinger := learning_adj_bollinger - adaptationRate * prediction_gradient * feature_bollinger
    learning_adj_stochastic := learning_adj_stochastic - adaptationRate * prediction_gradient * feature_stochastic
    learning_adj_williams := learning_adj_williams - adaptationRate * prediction_gradient * feature_williams
    learning_adj_fisher := learning_adj_fisher - adaptationRate * prediction_gradient * feature_fisher
    learning_adj_break_retest := learning_adj_break_retest - adaptationRate * prediction_gradient * feature_break_retest
    
    // Final Prediction with Enhanced Adapted Weights
    final_prediction = neural_activation_enhanced(feature_momentum, feature_volatility, feature_trend, feature_oscillation, feature_velocity, feature_resistance, feature_macd, feature_bollinger, feature_stochastic, feature_williams, feature_fisher, feature_break_retest, bias_node, current_momentum, current_volatility, current_trend, current_oscillation, current_velocity, current_resistance, current_macd, current_bollinger, current_stochastic, current_williams, current_fisher, current_break_retest)
    
    // Transform to Bipolar Signal Range (-1 to 1)
    ai_prediction := (final_prediction - 0.5) * 2

// =============================================================================
// ENHANCED SIGNAL FUSION WITH BREAK RETEST
// =============================================================================

// Calculate composite signal from additional indicators
additionalSignalsComposite = 0.0
signalCount = 0

if enableMACD
    additionalSignalsComposite += macdNormalized
    signalCount += 1

if enableBollinger
    additionalSignalsComposite += bb_position
    signalCount += 1

if enableStochastic
    additionalSignalsComposite += stochNormalized
    signalCount += 1

if enableWilliamsR
    additionalSignalsComposite += williamsNormalized
    signalCount += 1

if enableFisherTransform
    additionalSignalsComposite += fisher_transform
    signalCount += 1

if enableBreakRetest
    additionalSignalsComposite += br_signal
    signalCount += 1

additionalSignalsNormalized = signalCount > 0 ? additionalSignalsComposite / signalCount : 0

// Enhanced signal fusion
rawFinalSignal = 0.0
componentCount = 0

if useWavelet
    rawFinalSignal += waveletNormalized
    componentCount += 1

if enableAI
    rawFinalSignal += ai_prediction
    componentCount += 1

if signalCount > 0
    rawFinalSignal += additionalSignalsNormalized
    componentCount += 1

if componentCount == 0
    rawFinalSignal := ta.mom(close, 14) / close
else
    rawFinalSignal := rawFinalSignal / componentCount

// APPLY SMOOTHING TO ENTIRE FINAL SIGNAL 
smoothedFinalSignal = ta.ema(rawFinalSignal, smoothing)

// ENHANCED ADAPTIVE NORMALIZATION
recentVolatility = ta.stdev(smoothedFinalSignal, lookback)
longTermVolatility = ta.stdev(smoothedFinalSignal, lookback * 2)
adaptiveScale = recentVolatility / math.max(longTermVolatility, 0.0001)

// Apply adaptive scaling 
finalSignal = smoothedFinalSignal * adaptiveScale
finalSignal := math.min(math.max(finalSignal, -2), 2)  

// Enhanced signal strength calculation
signalStrength = math.abs(finalSignal)
signalMomentum = finalSignal - finalSignal[1]

// =============================================================================
// NEON GRID REFERENCE LINES
// =============================================================================

// Neon grid colors with glow effect
neonGridColor1 = showHLines ? color.new(#00ffff, gridGlow ? 40 : 70) : color.new(color.white, 100)
neonGridColor2 = showHLines ? color.new(#ff00ff, gridGlow ? 50 : 75) : color.new(color.white, 100)
neonGridColor3 = showHLines ? color.new(#ffff00, gridGlow ? 45 : 70) : color.new(color.white, 100)
zeroNeonColor = showHLines ? color.new(#ffffff, gridGlow ? 20 : 60) : color.new(color.white, 100)

// Pulsing effect for zero line
pulseAlpha = pulseEffect ? math.abs(math.sin(bar_index * 0.1)) * 40 + 20 : 30
zeroLinePulse = showHLines ? color.new(#00ff41, pulseAlpha) : color.new(color.white, 100)

plot(1.0, "🔥 STRONG BULL", color = neonGridColor2, linewidth = 1)
plot(0.5, "✨ BULL ZONE", color = neonGridColor1, linewidth = 1)
plot(0.25, "🌟 WEAK BULL", color = neonGridColor2, linewidth = 1)
plot(-0.25, "💫 WEAK BEAR", color = neonGridColor2, linewidth = 1)
plot(-0.5, "🔴 BEAR ZONE", color = neonGridColor1, linewidth = 1)
plot(-1.0, "🩸 STRONG BEAR", color = neonGridColor2, linewidth = 1)

// =============================================================================
// NEON VISUALIZATION WITH BREAK RETEST INTEGRATION
// =============================================================================

// Dynamic neon glow intensity based on signal strength
glowBase = math.round(glowIntensity * 100)
strongGlow = math.max(0, glowBase - 20)
mediumGlow = math.max(0, glowBase - 10)
weakGlow = glowBase

// Pulsing break/retest colors
breakPulse = pulseEffect ? math.abs(math.sin(bar_index * 0.15)) * 30 : 0
retestPulse = pulseEffect ? math.abs(math.cos(bar_index * 0.12)) * 25 : 0

// Dynamic neon color calculation with glow effects
dynamicColor = switch
    resistance_break_signal => color.new(break_col, breakPulse)
    support_break_signal => color.new(break_col, breakPulse)
    resistance_retest_signal => color.new(retest_col, retestPulse)
    support_retest_signal => color.new(retest_col, retestPulse)
    finalSignal > 0.75 => color.new(upper_col, strongGlow)
    finalSignal > 0.25 => color.new(upper_col, mediumGlow)
    finalSignal > 0 => color.new(upper_col, weakGlow)
    finalSignal > -0.25 => color.new(neutral_col, weakGlow)
    finalSignal > -0.75 => color.new(lower_col, weakGlow)
    => color.new(lower_col, strongGlow)

// Enhanced neon glow sizing based on signal strength
signalRatio = math.min(math.abs(finalSignal) * 2, 2)
candleThickness = math.max(1, math.round(signalRatio + 1))
signalGlow = math.max(0, 100 - (signalRatio * 25))

// Enhanced neon candle color calculation with break retest integration
candleColor = switch candleStyle
    "Neon Glow" =>
        if resistance_break_signal or support_break_signal
            color.new(break_col, breakPulse)
        else if resistance_retest_signal or support_retest_signal
            color.new(retest_col, retestPulse)
        else
            color.new(dynamicColor, signalGlow)
    "Electric" =>
        electricPulse = pulseEffect ? math.abs(math.sin(bar_index * 0.2)) * 40 : 20
        if resistance_break_signal or support_break_signal
            color.new(glow_col, electricPulse)
        else if resistance_retest_signal or support_retest_signal
            color.new(accent_col, electricPulse)
        else
            color.new(dynamicColor, electricPulse)
    "Enhanced" =>
        if resistance_break_signal or support_break_signal
            color.new(break_col, 10)
        else if resistance_retest_signal or support_retest_signal
            color.new(retest_col, 15)
        else
            dynamicColor
    "Gradient" =>
        if resistance_break_signal or support_break_signal
            color.new(break_col, breakPulse)
        else if resistance_retest_signal or support_retest_signal
            color.new(retest_col, retestPulse)
        else
            gradientIntensity = math.abs(finalSignal) * glowIntensity * 100
            color.new(dynamicColor, 100 - gradientIntensity)
    =>
        if resistance_break_signal or support_break_signal
            break_col
        else if resistance_retest_signal or support_retest_signal
            retest_col
        else
            finalSignal >= 0 ? upper_col : lower_col

// Neon wick and border effects
candleWickColor = switch candleStyle
    "Neon Glow" => color.new(glow_col, 30)
    "Electric" => color.new(accent_col, 40)
    => candleColor

candleBorderColor = switch candleStyle
    "Neon Glow" => color.new(candleColor, 10)
    "Electric" => color.new(glow_col, 20)
    "Enhanced" => color.new(candleColor, 20)
    "Gradient" => candleColor
    => candleColor

// Plot neon signal candles
plotcandle(open, high, low, close,
           title = '🌟 Neon Signal Candles',
           color = candleColor,
           wickcolor = candleWickColor,
           bordercolor = candleBorderColor,
           force_overlay=true)

// Neon signal line plotting with glow effects
mainSignalGlow = pulseEffect ? math.abs(math.sin(bar_index * 0.08)) * 20 : 10
mainSignalColor = switch
    resistance_break_signal or support_break_signal => color.new(break_col, breakPulse)
    resistance_retest_signal or support_retest_signal => color.new(retest_col, retestPulse)
    finalSignal > 1 => color.new(upper_col, mainSignalGlow)
    finalSignal > 0.5 => color.new(upper_col, mainSignalGlow + 10)
    finalSignal > 0 => color.new(upper_col, mainSignalGlow + 20)
    finalSignal > -0.5 => color.new(lower_col, mainSignalGlow + 20)
    finalSignal > -1 => color.new(lower_col, mainSignalGlow + 10)
    => color.new(lower_col, mainSignalGlow)

// Main neon signal line with enhanced thickness
signalLineWidth = math.max(2, math.round(3 * glowIntensity))
p1 = plot(finalSignal, "🌟 Neon Signal", color = mainSignalColor, linewidth = signalLineWidth)
p2 = plot(0, "━━━ Zero Line ━━━", linewidth = 2, color = zeroLinePulse)

// Neon gradient fill with glow effects
fillGlow = math.round(85 - (glowIntensity * 15))
fillPulse = pulseEffect ? math.abs(math.sin(bar_index * 0.05)) * 10 : 0

fillColor = switch
    resistance_break_signal or support_break_signal => color.new(break_col, 60 + fillPulse)
    resistance_retest_signal or support_retest_signal => color.new(retest_col, 65 + fillPulse)
    finalSignal > 0.5 => color.new(upper_col, fillGlow)
    finalSignal > 0 => color.new(upper_col, fillGlow + 5)
    finalSignal > -0.5 => color.new(lower_col, fillGlow + 5)
    => color.new(lower_col, fillGlow)

fill(p1, p2, finalSignal, 0, na, fillColor)

// Neon momentum pulse bars
momentumPulse = pulseEffect ? math.abs(math.cos(bar_index * 0.1)) * 20 : 30
momentumColor = signalMomentum > 0 ? color.new(upper_col, momentumPulse) : color.new(lower_col, momentumPulse)
momentumValue = showMomentum ? signalMomentum * 2 : na
momentumWidth = math.max(1, math.round(2 * glowIntensity))
plot(momentumValue, "💫 Momentum Pulse", color = momentumColor, style = plot.style_histogram, linewidth = momentumWidth)

// Enhanced signal arrows with break retest integration
strongBullishSignal = finalSignal > 0.5 and finalSignal[1] <= 0.5
weakBullishSignal = finalSignal > 0 and finalSignal[1] <= 0 and not strongBullishSignal
strongBearishSignal = finalSignal < -0.5 and finalSignal[1] >= -0.5
weakBearishSignal = finalSignal < 0 and finalSignal[1] >= 0 and not strongBearishSignal

// Break Retest specific signals for plotting
breakBullishSignal = resistance_break_signal and showBreakRetest
breakBearishSignal = support_break_signal and showBreakRetest
retestBullishSignal = resistance_retest_signal and showBreakRetest
retestBearishSignal = support_retest_signal and showBreakRetest

// Neon signal arrows with glow effects
plotshape(strongBullishSignal,
  style=shape.labelup,
  location=location.belowbar,
  color=color.new(upper_col, strongGlow),
  textcolor=glow_col,
  size=size.small,
  text="⭷",
  title="🌟 Strong Bull Neon", force_overlay=true)

plotshape(weakBullishSignal,
  style=shape.labelup,
  location=location.belowbar,
  color=color.new(upper_col, mediumGlow),
  textcolor=color.new(glow_col, 20),
  size=size.tiny,
  text="⬆",
  title="✨ Weak Bull Glow", force_overlay=true)

plotshape(strongBearishSignal,
  style=shape.labeldown,
  location=location.abovebar,
  color=color.new(lower_col, strongGlow),
  textcolor=glow_col,
  size=size.small,
  text="⭸",
  title="🌟 Strong Bear Neon", force_overlay=true)

plotshape(weakBearishSignal,
  style=shape.labeldown,
  location=location.abovebar,
  color=color.new(lower_col, mediumGlow),
  textcolor=color.new(glow_col, 20),
  size=size.tiny,
  text="⬇",
  title="✨ Weak Bear Glow", force_overlay=true)

// Neon Break Retest lines with glow effects
neonLineWidth = math.max(2, math.round(4 * glowIntensity))

plot(series=breakBullishSignal ? high : na,
     title="💥 Resistance Break Neon",
     color=color.new(break_col, breakPulse),
     linewidth=neonLineWidth,
     style=plot.style_linebr,
     trackprice=true,
     force_overlay=true)

plot(series=breakBearishSignal ? low : na,
     title="💥 Support Break Neon",
     color=color.new(break_col, breakPulse),
     linewidth=neonLineWidth,
     style=plot.style_linebr,
     trackprice=true,
     force_overlay=true)

plot(series=retestBullishSignal ? high : na,
     title="🔄 Resistance Retest Glow",
     color=color.new(retest_col, retestPulse),
     linewidth=neonLineWidth,
     style=plot.style_linebr,
     trackprice=true,
     force_overlay=true)

plot(series=retestBearishSignal ? low : na,
     title="🔄 Support Retest Glow",
     color=color.new(retest_col, retestPulse),
     linewidth=neonLineWidth,
     style=plot.style_linebr,
     trackprice=true,
     force_overlay=true)

// =============================================================================
// ENHANCED ALERT SYSTEM WITH BREAK RETEST
// =============================================================================
alertcondition(strongBullishSignal, "Strong Bullish Signal", "Strong Bullish Signal Detected - High Confidence")
alertcondition(weakBullishSignal, "Weak Bullish Signal", "Weak Bullish Signal Detected")
alertcondition(strongBearishSignal, "Strong Bearish Signal", "Strong Bearish Signal Detected - High Confidence")
alertcondition(weakBearishSignal, "Weak Bearish Signal", "Weak Bearish Signal Detected")
alertcondition(math.abs(finalSignal) > 1.5, "Extreme Signal", "Extreme Market Condition Detected")
alertcondition(ta.crossover(finalSignal, 0), "Zero Line Cross Up", "Signal Crossed Above Zero Line")
alertcondition(ta.crossunder(finalSignal, 0), "Zero Line Cross Down", "Signal Crossed Below Zero Line")

// Enhanced Break Retest specific alerts with strength classification
alertcondition(resistance_break_signal, "Resistance Break", "Resistance Level Broken - Bullish Breakout")
alertcondition(support_break_signal, "Support Break", "Support Level Broken - Bearish Breakdown")
alertcondition(resistance_retest_signal, "Resistance Retest", "Resistance Level Retested - Potential Reversal")
alertcondition(support_retest_signal, "Support Retest", "Support Level Retested - Potential Bounce")
alertcondition(breakBullishSignal or breakBearishSignal, "Break Signal", "Break Signal Detected - Structure Broken")
alertcondition(retestBullishSignal or retestBearishSignal, "Retest Signal", "Retest Signal Detected - Structure Retested")

// High-confidence alerts for strong levels
alertcondition(strong_resistance_break, "STRONG Resistance Break", "HIGH CONFIDENCE: Strong Resistance Broken with Volume!")
alertcondition(strong_support_break, "STRONG Support Break", "HIGH CONFIDENCE: Strong Support Broken with Volume!")
alertcondition(weak_break_warning, "Weak Break Warning", "WARNING: Break detected but with low volume - may be false breakout")

// =============================================================================
// 📊 INFORMATION PANEL (OPTIONAL)
// =============================================================================
showInfoTable = input.bool(true, "Show Info Panel", tooltip="Displays a live information panel showing:\n• Current resistance/support levels\n• Break/retest status\n• Signal strength\nUseful for monitoring key levels in real-time!", group="🌟 Neon Display")

if showInfoTable and barstate.islast
    // Neon-themed table with dark background and glowing borders
    var table infoTable = table.new(position.top_right, 2, 6,
                                   bgcolor=color.new(shadow_col, 10),
                                   border_width=2,
                                   border_color=color.new(break_col, 30))

    table.cell(infoTable, 0, 0, "🌟 STATUS PANEL", text_color=glow_col, text_size=size.normal, bgcolor=color.new(accent_col, 80))
    table.cell(infoTable, 1, 0, "", text_color=glow_col, text_size=size.small, bgcolor=color.new(accent_col, 80))

    table.cell(infoTable, 0, 1, "⚡ Resistance", text_color=color.new(break_col, 0), text_size=size.small, bgcolor=color.new(shadow_col, 20))
    resistance_text = str.tostring(recent_resistance, "#.####") + " (💪" + str.tostring(resistance_level_strength) + ")"
    table.cell(infoTable, 1, 1, resistance_text, text_color=glow_col, text_size=size.small, bgcolor=color.new(shadow_col, 20))

    table.cell(infoTable, 0, 2, "⚡ Support", text_color=color.new(retest_col, 0), text_size=size.small, bgcolor=color.new(shadow_col, 20))
    support_text = str.tostring(recent_support, "#.####") + " (💪" + str.tostring(support_level_strength) + ")"
    table.cell(infoTable, 1, 2, support_text, text_color=glow_col, text_size=size.small, bgcolor=color.new(shadow_col, 20))

    table.cell(infoTable, 0, 3, "🔥 R-Status", text_color=color.new(upper_col, 0), text_size=size.small, bgcolor=color.new(shadow_col, 20))
    res_status = resistance_broken ? (resistance_retested ? "💥 BROKEN & RETESTED" : strong_resistance_break ? "🚀 STRONG BREAK" : "💥 BROKEN") : "✨ INTACT"
    res_color = resistance_broken ? (strong_resistance_break ? color.new(break_col, 0) : color.new(lower_col, 0)) : color.new(upper_col, 0)
    table.cell(infoTable, 1, 3, res_status, text_color=res_color, text_size=size.small, bgcolor=color.new(shadow_col, 20))

    table.cell(infoTable, 0, 4, "🔥 S-Status", text_color=color.new(lower_col, 0), text_size=size.small, bgcolor=color.new(shadow_col, 20))
    sup_status = support_broken ? (support_retested ? "💥 BROKEN & RETESTED" : strong_support_break ? "� STRONG BREAK" : "�💥 BROKEN") : "✨ INTACT"
    sup_color = support_broken ? (strong_support_break ? color.new(break_col, 0) : color.new(lower_col, 0)) : color.new(upper_col, 0)
    table.cell(infoTable, 1, 4, sup_status, text_color=sup_color, text_size=size.small, bgcolor=color.new(shadow_col, 20))

    table.cell(infoTable, 0, 5, "⚡ Signal Power", text_color=color.new(neutral_col, 0), text_size=size.small, bgcolor=color.new(shadow_col, 20))
    signal_strength_text = str.tostring(math.abs(br_signal), "#.##") + " ⚡"
    table.cell(infoTable, 1, 5, signal_strength_text, text_color=glow_col, text_size=size.small, bgcolor=color.new(shadow_col, 20))

// Enhanced alert system for S/R levels
alertcondition(alerts.get("Retest"), "S/R Retest Alert", "Support/Resistance Level Retested!")
alertcondition(alerts.get("Break"), "S/R Break Alert", "Support/Resistance Level Broken!")

enableRetestAlerts = input.bool(true, "Enable S/R Retest Alerts", group="🔔 Alert Settings", tooltip="Get notified when S/R levels are retested")
enableBreakAlerts = input.bool(true, "Enable S/R Break Alerts", group="🔔 Alert Settings", tooltip="Get notified when S/R levels are broken")

if enableRetestAlerts and alerts.get("Retest")
    alert("🔄 New S/R Retest Detected!")

if enableBreakAlerts and alerts.get("Break")
    alert("💥 New S/R Break Detected!")